package org.jeecg.modules.demo.leetcodere.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.demo.leetcodere.entity.LeetcodeRewrite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 对于leetcode算法题的重写
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
public interface LeetcodeRewriteMapper extends BaseMapper<LeetcodeRewrite> {

}
