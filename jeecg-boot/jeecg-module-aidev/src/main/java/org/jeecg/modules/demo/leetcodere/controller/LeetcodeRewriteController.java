package org.jeecg.modules.demo.leetcodere.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.leetcodere.entity.LeetcodeRewrite;
import org.jeecg.modules.demo.leetcodere.service.ILeetcodeRewriteService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 对于leetcode算法题的重写
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Api(tags="对于leetcode算法题的重写")
@RestController
@RequestMapping("/leetcodere/leetcodeRewrite")
@Slf4j
public class LeetcodeRewriteController extends JeecgController<LeetcodeRewrite, ILeetcodeRewriteService> {
	@Autowired
	private ILeetcodeRewriteService leetcodeRewriteService;
	
	/**
	 * 分页列表查询
	 *
	 * @param leetcodeRewrite
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "对于leetcode算法题的重写-分页列表查询")
	@ApiOperation(value="对于leetcode算法题的重写-分页列表查询", notes="对于leetcode算法题的重写-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<LeetcodeRewrite>> queryPageList(LeetcodeRewrite leetcodeRewrite,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<LeetcodeRewrite> queryWrapper = QueryGenerator.initQueryWrapper(leetcodeRewrite, req.getParameterMap());
		Page<LeetcodeRewrite> page = new Page<LeetcodeRewrite>(pageNo, pageSize);
		IPage<LeetcodeRewrite> pageList = leetcodeRewriteService.page(page, queryWrapper);
		System.out.println("哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈");
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param leetcodeRewrite
	 * @return
	 */
	@AutoLog(value = "对于leetcode算法题的重写-添加")
	@ApiOperation(value="对于leetcode算法题的重写-添加", notes="对于leetcode算法题的重写-添加")
	@RequiresPermissions("leetcodere:leetcode_rewrite:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody LeetcodeRewrite leetcodeRewrite) {
		leetcodeRewriteService.save(leetcodeRewrite);
		return Result.OK("添加成功！");
	}

	 /**
	  *   处理题目
	  *
	  *
	  * @return
	  */
	 @AutoLog(value = "对于leetcode算法题的重写-处理题目")
	 @ApiOperation(value="对于leetcode算法题的重写-处理题目", notes="对于leetcode算法题的重写-处理题目")
//	 @RequiresPermissions("leetcodere:leetcode_rewrite:solve")
	 @PostMapping(value = "/solve")
	 public Result<String> solve() {
		 log.info("对于lc算法题重写处理题目接口的调用");

		 leetcodeRewriteService.solve();
		 return Result.OK("处理题目成功");
	 }
	
	/**
	 *  编辑
	 *
	 * @param leetcodeRewrite
	 * @return
	 */
	@AutoLog(value = "对于leetcode算法题的重写-编辑")
	@ApiOperation(value="对于leetcode算法题的重写-编辑", notes="对于leetcode算法题的重写-编辑")
	@RequiresPermissions("leetcodere:leetcode_rewrite:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody LeetcodeRewrite leetcodeRewrite) {
		leetcodeRewriteService.updateById(leetcodeRewrite);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "对于leetcode算法题的重写-通过id删除")
	@ApiOperation(value="对于leetcode算法题的重写-通过id删除", notes="对于leetcode算法题的重写-通过id删除")
	@RequiresPermissions("leetcodere:leetcode_rewrite:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		leetcodeRewriteService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "对于leetcode算法题的重写-批量删除")
	@ApiOperation(value="对于leetcode算法题的重写-批量删除", notes="对于leetcode算法题的重写-批量删除")
	@RequiresPermissions("leetcodere:leetcode_rewrite:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.leetcodeRewriteService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "对于leetcode算法题的重写-通过id查询")
	@ApiOperation(value="对于leetcode算法题的重写-通过id查询", notes="对于leetcode算法题的重写-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<LeetcodeRewrite> queryById(@RequestParam(name="id",required=true) String id) {
		LeetcodeRewrite leetcodeRewrite = leetcodeRewriteService.getById(id);
		if(leetcodeRewrite==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(leetcodeRewrite);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param leetcodeRewrite
    */
    @RequiresPermissions("leetcodere:leetcode_rewrite:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LeetcodeRewrite leetcodeRewrite) {
        return super.exportXls(request, leetcodeRewrite, LeetcodeRewrite.class, "对于leetcode算法题的重写");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LeetcodeRewrite.class);
    }

}
