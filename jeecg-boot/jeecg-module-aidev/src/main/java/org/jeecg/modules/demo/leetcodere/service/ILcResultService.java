package org.jeecg.modules.demo.leetcodere.service;

import org.jeecg.modules.demo.leetcodere.entity.LcResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 存储AI处理题目后的结果
 * @Author: jeecg-boot
 * @Date:   2025-03-29
 * @Version: V1.0
 */
public interface ILcResultService extends IService<LcResult> {

    /**
     * 上传处理题目到平台
     * @return
     */
    String platformUpdate();

}
