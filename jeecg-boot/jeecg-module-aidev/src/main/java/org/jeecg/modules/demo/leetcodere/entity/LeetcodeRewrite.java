package org.jeecg.modules.demo.leetcodere.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 对于leetcode算法题的重写
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Data
@TableName("leetcode_rewrite")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="leetcode_rewrite对象", description="对于leetcode算法题的重写")
public class LeetcodeRewrite implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**题目序号*/
	@Excel(name = "题目序号", width = 15)
    @ApiModelProperty(value = "题目序号")
    private java.lang.String quesId;
	/**题目slug*/
	@Excel(name = "题目slug", width = 15)
    @ApiModelProperty(value = "题目slug")
    private java.lang.String quesSlug;
    /**录入类型*/
    @Excel(name = "题目类别", width = 15)
    @ApiModelProperty(value = "问题类型")
    private java.lang.String quesType;
	/**录入类型*/
	@Excel(name = "录入状态", width = 15)
    @ApiModelProperty(value = "录入类型")
    private java.lang.String insertStatus;
	/**验收状态*/
	@Excel(name = "验收状态", width = 15)
    @ApiModelProperty(value = "验收状态")
    private java.lang.String validStatus;
	/**验收失败原因*/
	@Excel(name = "验收失败原因", width = 15)
    @ApiModelProperty(value = "验收失败原因")
    private java.lang.String validFailedReason;
	/**验收人*/
	@Excel(name = "验收人", width = 15)
    @ApiModelProperty(value = "验收人")
    private java.lang.String valider;
	/**验收问题状态*/
	@Excel(name = "验收问题状态", width = 15)
    @ApiModelProperty(value = "验收问题状态")
    private java.lang.String validIssue;
	/**是否最终验收*/
	@Excel(name = "最终验收通过", width = 15)
    @ApiModelProperty(value = "是否最终验收")
    private java.lang.String isFinalValid;
	/**录入描述*/
	@Excel(name = "录入描述", width = 15)
    @ApiModelProperty(value = "录入描述")
    private java.lang.String insertDescribe;
}
