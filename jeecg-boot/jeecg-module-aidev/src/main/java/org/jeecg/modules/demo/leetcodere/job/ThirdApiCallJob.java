package org.jeecg.modules.demo.leetcodere.job;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @create 2025-03-29 19:35
 */
public class ThirdApiCallJob implements Job {

    @Autowired
    RestTemplate restTemplate;


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

    }
}
