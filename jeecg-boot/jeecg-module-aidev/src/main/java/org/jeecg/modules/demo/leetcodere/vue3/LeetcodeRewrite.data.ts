import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '题目序号',
    align:"center",
    dataIndex: 'quesId'
   },
   {
    title: '题目slug',
    align:"center",
    dataIndex: 'quesSlug'
   },
   {
    title: '录入类型',
    align:"center",
    dataIndex: 'insertType_dictText'
   },
   {
    title: '验收状态',
    align:"center",
    dataIndex: 'validStatus'
   },
   {
    title: '验收失败原因',
    align:"center",
    dataIndex: 'validFailedReason'
   },
   {
    title: '验收人',
    align:"center",
    dataIndex: 'valider'
   },
   {
    title: '验收问题状态',
    align:"center",
    dataIndex: 'validIssue'
   },
   {
    title: '是否最终验收',
    align:"center",
    dataIndex: 'isFinalValid_dictText'
   },
   {
    title: '录入描述',
    align:"center",
    dataIndex: 'insertDescribe'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '题目序号',
    field: 'quesId',
    component: 'Input',
  },
  {
    label: '题目slug',
    field: 'quesSlug',
    component: 'Input',
  },
  {
    label: '录入类型',
    field: 'insertType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '验收状态',
    field: 'validStatus',
    component: 'Input',
  },
  {
    label: '验收失败原因',
    field: 'validFailedReason',
    component: 'Input',
  },
  {
    label: '验收人',
    field: 'valider',
    component: 'Input',
  },
  {
    label: '验收问题状态',
    field: 'validIssue',
    component: 'Input',
  },
  {
    label: '是否最终验收',
    field: 'isFinalValid',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '录入描述',
    field: 'insertDescribe',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  quesId: {title: '题目序号',order: 0,view: 'text', type: 'string',},
  quesSlug: {title: '题目slug',order: 1,view: 'text', type: 'string',},
  insertType: {title: '录入类型',order: 2,view: 'list', type: 'string',dictCode: '',},
  validStatus: {title: '验收状态',order: 3,view: 'text', type: 'string',},
  validFailedReason: {title: '验收失败原因',order: 4,view: 'text', type: 'string',},
  valider: {title: '验收人',order: 5,view: 'text', type: 'string',},
  validIssue: {title: '验收问题状态',order: 6,view: 'text', type: 'string',},
  isFinalValid: {title: '是否最终验收',order: 7,view: 'list', type: 'string',dictCode: '',},
  insertDescribe: {title: '录入描述',order: 8,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}