package org.jeecg.modules.demo.leetcodere.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.demo.leetcodere.constant.HttpConstant;
import org.jeecg.modules.demo.leetcodere.entity.LcResult;
import org.jeecg.modules.demo.leetcodere.entity.LeetcodeRewrite;
import org.jeecg.modules.demo.leetcodere.mapper.LcResultMapper;
import org.jeecg.modules.demo.leetcodere.service.ILcResultService;
import org.jeecg.modules.demo.leetcodere.service.ILeetcodeRewriteService;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 存储AI处理题目后的结果
 * @Author: jeecg-boot
 * @Date:   2025-03-29
 * @Version: V1.0
 */
@Service
public class LcResultServiceImpl extends ServiceImpl<LcResultMapper, LcResult> implements ILcResultService {

    @Resource
    ILeetcodeRewriteService leetcodeRewriteService;

    @Resource
    RestTemplate restTemplate;


    @Override
    public String platformUpdate() {
        //1.从数据库拉取最新的题目
        LeetcodeRewrite rewrite = leetcodeRewriteService.recentId();
        String quesId = rewrite.getQuesId();

        //2.获取并且处理内容
        LambdaQueryWrapper<LcResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LcResult::getQuesId,quesId);

        LcResult lcResult = this.getOne(wrapper);
        if(null == lcResult){
            rewrite.setInsertDescribe("原题为空");
            leetcodeRewriteService.updateById(rewrite);
            return "1";
        }

        String content = lcResult.getContent();


        List<String> languageContent = extractLanguageContent(content);

        //3.拉取对应题目id的题目内容，阻塞两分钟后上传。

        String api1Url = String.format("https://api-mgt.drillinsight.com/mgt-api/questionbank/v2/questionInfo/get?id=%s", quesId);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", HttpConstant.TOKEN_PREFIX + HttpConstant.TOKEN);
        HttpEntity<String> requestGet = new HttpEntity<>(headers);
        ResponseEntity<String> res = restTemplate.exchange(api1Url, HttpMethod.GET, requestGet, String.class);
        JSONObject json = JSONUtil.parseObj(res.getBody());
        JSONObject data = json.getJSONObject("data");


        Random random = new Random();
        int base = 300000;
        int fluctuation = 15000;

        // 生成 [900, 1500] 的随机数
        int delayTime = base - fluctuation + random.nextInt(2 * fluctuation + 1);

        try {
            Thread.sleep(delayTime);
        } catch (InterruptedException e) {
            e.printStackTrace();
            return "error";
        }

        String api2Url = String.format("https://api-mgt.drillinsight.com/mgt-api/questionbank/v2/questionInfo/update");
        headers.setContentType(MediaType.APPLICATION_JSON); // 设置Content-Type
        // 创建请求体对象 TODO

        JSONObject request = new JSONObject();
        request.set("categoryIds", data.getJSONArray("categoryIds"));
        request.set("description_en-US", languageContent.get(1));
        request.set("description_zh-CN", languageContent.get(0));
        request.set("difficultyId", data.getInt("difficultyId"));
        request.set("fileList", data.getJSONArray("fileList"));
        request.set("id", data.getInt("id"));
        request.set("questionType", data.getStr("questionType"));
        request.set("slug", data.getStr("slug"));
        request.set("status", data.getInt("status"));
        request.set("tagIds", data.getJSONArray("tagIds"));
        request.set("topicIds", data.getJSONArray("topicIds"));

        JSONArray similarQuestions = data.getJSONArray("similarQuestions");
        List<Long> similarQuestionIds = new ArrayList<>();
        if (similarQuestions != null) {
            for (int i = 0; i < similarQuestions.size(); i++) {
                similarQuestionIds.add(similarQuestions.getJSONObject(i).getLong("id"));
            }
        }
        request.set("similarQuestions", similarQuestionIds);


        String requestBody = JSONUtil.toJsonStr(request);

        HttpEntity<String> requestUp = new HttpEntity<>(requestBody,headers);

        restTemplate.exchange(api2Url,HttpMethod.POST,requestUp,String.class);

        //TODO 需要修改代码为循环查找，关机代表下线。
        rewrite.setInsertDescribe("1");

        leetcodeRewriteService.updateById(rewrite);


        return "1";
    }


    private List<String> extractLanguageContent(String html) {
        return Arrays.asList(
                extractChineseContent(html),
                extractEnglishContent(html)
        );
    }

    private String extractChineseContent(String html) {
        // 匹配从"中文"开始到"English"或文件末尾的内容
        Pattern pattern = Pattern.compile(
                "(?si)(.*?中文[^<]*)(<.*?>)(.*?)(?=English|$)",
                Pattern.DOTALL
        );
        return extractContent(html, pattern);
    }

    private String extractEnglishContent(String html) {
        // 匹配从"English"开始到文件末尾的内容
        Pattern pattern = Pattern.compile(
                "(?si)(.*?English[^<]*)(<.*?>)(.*?)(?=$)",
                Pattern.DOTALL
        );
        return extractContent(html, pattern);
    }

    private String extractContent(String html, Pattern pattern) {
        Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            // 返回从第一个HTML标签开始的内容
            return (matcher.group(2) + matcher.group(3)).trim();
        }
        return "";
    }
}
