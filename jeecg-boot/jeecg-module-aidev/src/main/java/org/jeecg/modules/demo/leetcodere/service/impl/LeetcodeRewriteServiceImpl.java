package org.jeecg.modules.demo.leetcodere.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.util.validation.feature.DatabaseType;
import org.jeecg.chatgpt.dto.chat.MultiChatMessage;
import org.jeecg.chatgpt.service.AiChatService;
import org.jeecg.modules.demo.leetcodere.entity.LeetcodeRewrite;
import org.jeecg.modules.demo.leetcodere.mapper.LeetcodeRewriteMapper;
import org.jeecg.modules.demo.leetcodere.service.IApiProcessorService;
import org.jeecg.modules.demo.leetcodere.service.ILeetcodeRewriteService;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * @Description: 对于leetcode算法题的重写
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Slf4j
@Service
public class LeetcodeRewriteServiceImpl extends ServiceImpl<LeetcodeRewriteMapper, LeetcodeRewrite> implements ILeetcodeRewriteService {

    @Autowired
    ISysDictService sysDictService;

    @Resource
    IApiProcessorService apiProcessorService;

    @Override
    public void solve() {

        LambdaQueryWrapper<LeetcodeRewrite> wrapper = new LambdaQueryWrapper<LeetcodeRewrite>().isNull(LeetcodeRewrite::getValidStatus).apply("ques_id NOT IN (SELECT ques_id FROM lc_result)");;

        List<LeetcodeRewrite> list = this.list(wrapper);

        List<String> ids = list.stream().map(LeetcodeRewrite::getQuesId).collect(Collectors.toList());

        apiProcessorService.processIds(ids);


    }

    @Override
    public LeetcodeRewrite recentId() {
        //1.从数据库拉取最新的题目
        LambdaQueryWrapper<LeetcodeRewrite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LeetcodeRewrite::getInsertStatus, "录入中")
                .isNull(LeetcodeRewrite::getInsertDescribe)
                .orderByDesc(LeetcodeRewrite::getCreateTime)
                .last("LIMIT 1");

        return this.getOne(wrapper);

    }

}
