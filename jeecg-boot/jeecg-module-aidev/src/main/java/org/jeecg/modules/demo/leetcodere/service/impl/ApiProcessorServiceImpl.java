package org.jeecg.modules.demo.leetcodere.service.impl;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.chatgpt.dto.chat.MultiChatMessage;
import org.jeecg.chatgpt.service.AiChatService;
import org.jeecg.modules.demo.leetcodere.constant.HttpConstant;
import org.jeecg.modules.demo.leetcodere.entity.LcResult;
import org.jeecg.modules.demo.leetcodere.service.IApiProcessorService;
import org.jeecg.modules.demo.leetcodere.service.ILcResultService;
import org.jeecg.modules.demo.leetcodere.service.ILeetcodeRewriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @create 2025-03-29 20:01
 */
@Service
@Slf4j
public class ApiProcessorServiceImpl implements IApiProcessorService {

    @Resource
    RestTemplate restTemplate;



//    @Resource
//    ILcResultService lcResultService;

    @Resource
    AiChatService aiChatService;


    @Resource(name = "apiCallThreadPool")
    private ExecutorService threadPool;

    @Override
    public void processIds(List<String> ids) {
        CompletableFuture<?>[] futures = ids.stream()
                .map(id -> CompletableFuture.runAsync(() -> processSingleId(id), threadPool))
                .toArray(CompletableFuture[]::new);

        CompletableFuture.allOf(futures).join();
    }

    private void processSingleId(String id) {
        try {
            // 第一步：调用API1查询基础信息
            String api1Url = String.format("https://api-mgt.drillinsight.com/mgt-api/questionbank/v2/questionInfo/get?id=%s", id);
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", HttpConstant.TOKEN_PREFIX + HttpConstant.TOKEN);
            HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<String> res = restTemplate.exchange(api1Url, HttpMethod.GET, request, String.class);
            JSONObject json = JSONUtil.parseObj(res.getBody());
            JSONObject data = json.getJSONObject("data");
            String quesDescribe = data.getStr("description_en-US");

            // 第二步：用API1结果调用API2
            String content = aiChat(quesDescribe);

//            boolean save = lcResultService.save(new LcResult().setContent(content).setQuesId(id));




        } catch (Exception e) {
            log.error("处理ID {} 失败: {}", id, e.getMessage());
        }
    }

    private String aiChat(String info){

//        JSONObject jsonObject = quesInfo.getJSONObject(0);
//        System.out.println("格式化输出:\n" + JSONUtil.toJsonPrettyStr(jsonObject));
//        System.out.println("Id为：" + jsonObject.get("quesId"));
        //一个回答的初始化系统消息，决定了整个回答的主要基调和边界
        MultiChatMessage sysMessage = MultiChatMessage.builder().role(MultiChatMessage.Role.USER).content("给你一个算法题的描述，请你根据简要的描述扩展为完整的题目，我会给你中文的描述，你需要补充出来中英文两个版本的题目的背景，题目可能存在的约束条件，并且需要给出中文版本对应的英文版本。\n" +
                "给出的描述文本范例（中英文两个版本）：\n" +
                "给定两个列表： jobs：一个 Job 列表，每个 Job 有一个唯一的 jobId 和一个标签列表 tags。 workers：一个 Worker 列表，每个 Worker 有一个唯一的 workerId 和一个标签列表 tags。 你需要找到所有匹配的 Job 和 Worker 对，其中 Job 和 Worker 至少有一个共同的标签。每个 jobId 和 workerId 只能被使用一次。 返回一个列表，包含所有匹配的 (jobId, workerId) 对，顺序不限。\n" +
                "要求如下：\n" +
                "1.第一段需要你描述完整题目的输入，比如示例中为两个列表，你应该注意其格式，比如是几维数组，数组内元素是什么基本类型（包括字符串类型），并且给出名称如 jobs 与workers ，并且需要描述输入数组内层的元素有什么意义，在题目中需要注意些什么。\n" +
                "2.如果题目对于返回结果有特别的提示，需要用”注意：“来标注出来，在第三段需要指明返回的结果是否有顺序要求（不限的时候才写出来），最大最小至多至少这些信息如果有都需要标注出来。\n" +
                "3.对于题目的测试用例来说需要隔一段，并且”示例“标题为字体为paragraph，内部的输入输出和描述字体格式为preformatted，对于测试用例来说，有格式的要返回三个（一共10个，其余的需要你在后面以格式给出，下文会提及具体的格式），一般第一个测试用例用于测试最一般的输入输出，第二个测试用例用于测试题目的边界条件，如空值，最小值，最极端情况，第三个测试用例如果题目中有出现的冲突情况，可以用来描述冲突的情况下如何选择\n" +
                "4.格式要求：对于题目的输入输出以及输入数组或输出数组的子元素都需要用<code>标签包裹起来，并且和前后文空一格出来，像是在中文描述中出现的数字和英文字母单词都需要和前后文空一格出来，像是题目描述中的关键信息，如下标从0开始，不规则二维，唯一，最大，最小，至多，至少，等能影响到人读题的关键信息都需要加粗，并且也要和前后文空一格出来。\n" +
                "5.最后的条件约束，按照列表格式描述，需要你给出输入的边界条件，比如是否为空，数组长度，至多至少，题目是否保证有解（自行判断），返回的输出如果为浮点数，其误差应该在10的-5次方以内等。示例：<code>10<sup>-5</sup></code>\n" +
                "--给你描述文本所应该出的格式的范例：\n" +
                "<p>给你两个 <strong>下标从0开始</strong>（包含内层）的 <strong>不规则二维</strong> 的字符串数组&nbsp;<code>jobs</code> 和 <code>workers</code>，其中 jobs 数组 和 workers 数组的索引 <code>i</code> 与 索引 <code>j</code> 代表的是每个 job 和 worker的 job_id 与 worker_id ，每个&nbsp;<code>jobs[i]</code> 和 <code>workers[j]</code> 表示的对应的 job 和 worker 的标签列表。</p>\n" +
                "<p>现在你负责工作上的安排，需要找到所有相互&nbsp;<strong>唯一&nbsp;</strong>匹配的 job 和 worker ，匹配的条件为这些 job 和 worker 都&nbsp;<strong>至少&nbsp;</strong>有一个共同的标签。返回一个二维数组 <code>matched</code> ，其中 <code>matched[i]</code> 为匹配到的 <strong>[job_id,worker_id]</strong>，不限返回的顺序。</p>\n" +
                "<p>注意：返回的结果中不应该包含重复的 job_id 和 worker_id。如果同一个 job 或者 worker 有多个匹配项，取 id 值&nbsp;<strong>最小&nbsp;</strong>的匹配项。</p>\n" +
                "<p>&nbsp;</p>\n" +
                "<p><strong>示例 1：</strong></p>\n" +
                "<pre><strong>输入：</strong>jobs = [[\"consistent\",\"precise\",\"adaptable\"],[\"resilient\",\"logical\"],[\"impactful\",\"rushed\"]], <br>       workers = [[\"rushed\"],[\"innovative\"],[\"resilient\",\"logical\",\"consistent\"]]\n" +
                "<strong>输出：</strong>[[1,3][2,0]]</pre>\n" +
                "<p><strong>示例 2：</strong></p>\n" +
                "<pre><strong>输入：</strong>jobs = [[\"proactive\"]], <br>       workers = [[\"proactive\"],[\"logical\"]]\n" +
                "<strong>输出：</strong>[[0,0]]</pre>\n" +
                "<p><strong>示例 3：</strong></p>\n" +
                "<pre><strong>输入：</strong>jobs = [[\"rushed\",\"scalable\"],[\"resilient\",\"logical\",\"consistent\"],[\"impactful\",\"adaptable\"]], <br>       workers = [[\"precise\",\"consistent\",\"impactful\"]]\n" +
                "<strong>输出：</strong>[[1,0]]</pre>\n" +
                "<p>&nbsp;</p>\n" +
                "<p><strong>提示：</strong></p>\n" +
                "<ul>\n" +
                "<li>题目保证至少存在一个匹配的 job_id 和 worker_id</li>\n" +
                "<li><code>jobs</code> 和 <code>workers</code> 的内层子元素不存在空元素</li>\n" +
                "<li>每个<code>jobs</code> 和 <code>workers</code>都至少有一行。</li>\n" +
                "</ul>\n" +
                "--结束范例").build();
        MultiChatMessage userMessage = MultiChatMessage.builder().role(MultiChatMessage.Role.USER).content(info).build();
        String aiResp = aiChatService.multiCompletions(Arrays.asList(sysMessage, userMessage));
        return aiResp;
    }
}
