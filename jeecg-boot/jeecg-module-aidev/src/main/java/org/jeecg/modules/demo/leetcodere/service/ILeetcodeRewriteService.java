package org.jeecg.modules.demo.leetcodere.service;

import org.jeecg.modules.demo.leetcodere.entity.LeetcodeRewrite;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 对于leetcode算法题的重写
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
public interface ILeetcodeRewriteService extends IService<LeetcodeRewrite> {

    void solve();

    /**
     * 从lcre表中拉取最新的符合条件的记录id
     * @return
     */
    LeetcodeRewrite recentId();
}
