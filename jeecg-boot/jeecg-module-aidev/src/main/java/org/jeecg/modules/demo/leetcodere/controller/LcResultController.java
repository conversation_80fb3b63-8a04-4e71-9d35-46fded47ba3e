package org.jeecg.modules.demo.leetcodere.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.leetcodere.entity.LcResult;
import org.jeecg.modules.demo.leetcodere.service.ILcResultService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 存储AI处理题目后的结果
 * @Author: jeecg-boot
 * @Date:   2025-03-29
 * @Version: V1.0
 */
@Api(tags="存储AI处理题目后的结果")
@RestController
@RequestMapping("/leetcodere/lcResult")
@Slf4j
public class LcResultController extends JeecgController<LcResult, ILcResultService> {
	@Autowired
	private ILcResultService lcResultService;
	
	/**
	 * 分页列表查询
	 *
	 * @param lcResult
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "存储AI处理题目后的结果-分页列表查询")
	@ApiOperation(value="存储AI处理题目后的结果-分页列表查询", notes="存储AI处理题目后的结果-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<LcResult>> queryPageList(LcResult lcResult,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<LcResult> queryWrapper = QueryGenerator.initQueryWrapper(lcResult, req.getParameterMap());
		Page<LcResult> page = new Page<LcResult>(pageNo, pageSize);
		IPage<LcResult> pageList = lcResultService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param lcResult
	 * @return
	 */
	@AutoLog(value = "存储AI处理题目后的结果-添加")
	@ApiOperation(value="存储AI处理题目后的结果-添加", notes="存储AI处理题目后的结果-添加")
	@RequiresPermissions("leetcodere:lc_result:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody LcResult lcResult) {
		lcResultService.save(lcResult);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param lcResult
	 * @return
	 */
	@AutoLog(value = "存储AI处理题目后的结果-编辑")
	@ApiOperation(value="存储AI处理题目后的结果-编辑", notes="存储AI处理题目后的结果-编辑")
	@RequiresPermissions("leetcodere:lc_result:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody LcResult lcResult) {
		lcResultService.updateById(lcResult);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "存储AI处理题目后的结果-通过id删除")
	@ApiOperation(value="存储AI处理题目后的结果-通过id删除", notes="存储AI处理题目后的结果-通过id删除")
	@RequiresPermissions("leetcodere:lc_result:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		lcResultService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "存储AI处理题目后的结果-批量删除")
	@ApiOperation(value="存储AI处理题目后的结果-批量删除", notes="存储AI处理题目后的结果-批量删除")
	@RequiresPermissions("leetcodere:lc_result:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.lcResultService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "存储AI处理题目后的结果-通过id查询")
	@ApiOperation(value="存储AI处理题目后的结果-通过id查询", notes="存储AI处理题目后的结果-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<LcResult> queryById(@RequestParam(name="id",required=true) String id) {
		LcResult lcResult = lcResultService.getById(id);
		if(lcResult==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(lcResult);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param lcResult
    */
    @RequiresPermissions("leetcodere:lc_result:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LcResult lcResult) {
        return super.exportXls(request, lcResult, LcResult.class, "存储AI处理题目后的结果");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
	@ApiOperation(value="存储AI处理题目后的结果-通过excel导入", notes="存储AI处理题目后的结果-通过excel导入")
//    @RequiresPermissions("leetcodere:lc_result:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LcResult.class);
    }


	 /**
	  * 上传题目到平台
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @ApiOperation(value="存储AI处理题目后的结果-上传题目到平台", notes="存储AI处理题目后的结果-上传题目到平台")
//    @RequiresPermissions("leetcodere:lc_result:importExcel")
	 @RequestMapping(value = "/platformUpdate", method = RequestMethod.GET)
	 public Result<?> platformUpdate(HttpServletRequest request, HttpServletResponse response) {
	 	String check = "1";
		 while ("1".equals(check)){
		 	check = lcResultService.platformUpdate();
		 }
		 return Result.ok();
	 }

}
