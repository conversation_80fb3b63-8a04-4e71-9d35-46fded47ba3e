package org.jeecg.config.threadpool;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @create 2025-03-29 19:56
 */
@Configuration
public class ThreadPoolConfig {
    @Bean("apiCallThreadPool")
    public ExecutorService apiCallThreadPool() {
        return Executors.newFixedThreadPool(5); // 根据服务器配置调整
    }
}
