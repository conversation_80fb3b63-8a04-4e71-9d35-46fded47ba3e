<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-server-cloud</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.7.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-visual</artifactId>
    <packaging>pom</packaging>
    
    <modules>
        <!-- 微服务所需要的中间件 -->
        <module>jeecg-cloud-sentinel</module>
        <module>jeecg-cloud-monitor</module>
        <module>jeecg-cloud-xxljob</module>
        <!-- 微服务测试示例-->
        <module>jeecg-cloud-test</module>
    </modules>


</project>