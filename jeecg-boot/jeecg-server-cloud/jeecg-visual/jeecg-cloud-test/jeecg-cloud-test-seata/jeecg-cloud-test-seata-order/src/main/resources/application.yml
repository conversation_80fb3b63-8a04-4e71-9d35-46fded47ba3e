server:
  port: 5001
spring:
  application:
    name: seata-order
  main:
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude: com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************
    username: root
    password: root
    schema: classpath:sql/schema-order.sql
seata:
#  enable-auto-data-source-proxy: false
  service:
    grouplist:
      default: 127.0.0.1:8091
    vgroup-mapping:
      springboot-seata-group: default
  # seata 事务组编号 用于TC集群名
  tx-service-group: springboot-seata-group