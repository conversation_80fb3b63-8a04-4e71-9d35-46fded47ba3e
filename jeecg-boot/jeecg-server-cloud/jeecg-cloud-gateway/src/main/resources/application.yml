server:
  port: 9999

spring:
  application:
    name: jeecg-gateway
  main:
    allow-circular-references: true
  config:
    import:
      - optional:nacos:${spring.application.name}-@profile.name@.yaml
  cloud:
    nacos:
      config:
        server-addr: @config.server-addr@
        group: @config.group@
        namespace: @config.namespace@
        username: @config.username@
        password: @config.password@
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        group: @config.group@
        namespace: @config.namespace@
        username: @config.username@
        password: @config.password@
    gateway:
      discovery:
        locator:
          enabled: true
      globalcors:
        cors-configurations:
          '[/**]':
            allowCredentials: true
            #springboot2.4后需用allowedOriginPatterns
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
    #Sentinel配置
    sentinel:
      transport:
        dashboard: jeecg-boot-sentinel:9000
      # 支持链路限流
      web-context-unify: false
      filter:
        enabled: false
      # 取消Sentinel控制台懒加载
      eager: false
      datasource:
        #流控规则
        flow:  # 指定数据源名称
          # 指定nacos数据源
          nacos:
            server-addr: @config.server-addr@
            # 指定配置文件
            dataId: ${spring.application.name}-flow-rules
            # 指定分组
            groupId: SENTINEL_GROUP
            # 指定配置文件规则类型
            rule-type: flow
            # 指定配置文件数据格式
            data-type: json
        #降级规则
        degrade:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-degrade-rules
            groupId: SENTINEL_GROUP
            rule-type: degrade
            data-type: json
        #系统规则
        system:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-system-rules
            groupId: SENTINEL_GROUP
            rule-type: system
            data-type: json
        #授权规则
        authority:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-authority-rules
            groupId: SENTINEL_GROUP
            rule-type: authority
            data-type: json
        #热点参数
        param-flow:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-param-rules
            groupId: SENTINEL_GROUP
            rule-type: param-flow
            data-type: json
        #网关流控规则
        gw-flow:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: gw-flow
            data-type: json
        #API流控规则
        gw-api-group:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-api-rules
            groupId: SENTINEL_GROUP
            rule-type: gw-api-group
            data-type: json