<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jeecg-cloud-nacos</artifactId>
    <name>jeecg-cloud-nacos</name>
    <description>nacos启动模块</description>
    <version>3.7.3</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <repositories>
        <repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <log4j2.version>2.17.0</log4j2.version>
        <dm8.version>********</dm8.version>
        <nacos.version>2.3.2</nacos.version>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.nacos</groupId>
            <artifactId>nacos-naming</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.nacos</groupId>
            <artifactId>nacos-istio</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.nacos</groupId>
            <artifactId>nacos-config</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.nacos</groupId>
            <artifactId>nacos-console</artifactId>
            <version>${nacos.version}</version>
        </dependency>

        <!--达梦数据库驱动 版本号1-3-26-2023.07.26-197096-20046-ENT -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>Dm8JdbcDriver18</artifactId>
            <version>${dm8.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmDialect-for-hibernate5.0</artifactId>
            <version>${dm8.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
