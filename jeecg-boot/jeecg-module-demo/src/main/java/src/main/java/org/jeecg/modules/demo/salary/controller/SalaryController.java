package org.jeecg.modules.demo.salary.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.salary.entity.Salary;
import org.jeecg.modules.demo.salary.service.ISalaryService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: salary
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="salary")
@RestController
@RequestMapping("/salary/salary")
@Slf4j
public class SalaryController extends JeecgController<Salary, ISalaryService> {
	@Autowired
	private ISalaryService salaryService;
	
	/**
	 * 分页列表查询
	 *
	 * @param salary
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "salary-分页列表查询")
	@ApiOperation(value="salary-分页列表查询", notes="salary-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Salary>> queryPageList(Salary salary,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Salary> queryWrapper = QueryGenerator.initQueryWrapper(salary, req.getParameterMap());
		Page<Salary> page = new Page<Salary>(pageNo, pageSize);
		IPage<Salary> pageList = salaryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param salary
	 * @return
	 */
	@AutoLog(value = "salary-添加")
	@ApiOperation(value="salary-添加", notes="salary-添加")
	@RequiresPermissions("salary:salary:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Salary salary) {
		salaryService.save(salary);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param salary
	 * @return
	 */
	@AutoLog(value = "salary-编辑")
	@ApiOperation(value="salary-编辑", notes="salary-编辑")
	@RequiresPermissions("salary:salary:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Salary salary) {
		salaryService.updateById(salary);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "salary-通过id删除")
	@ApiOperation(value="salary-通过id删除", notes="salary-通过id删除")
	@RequiresPermissions("salary:salary:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		salaryService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "salary-批量删除")
	@ApiOperation(value="salary-批量删除", notes="salary-批量删除")
	@RequiresPermissions("salary:salary:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.salaryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "salary-通过id查询")
	@ApiOperation(value="salary-通过id查询", notes="salary-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Salary> queryById(@RequestParam(name="id",required=true) String id) {
		Salary salary = salaryService.getById(id);
		if(salary==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(salary);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param salary
    */
    @RequiresPermissions("salary:salary:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Salary salary) {
        return super.exportXls(request, salary, Salary.class, "salary");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("salary:salary:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Salary.class);
    }

}
