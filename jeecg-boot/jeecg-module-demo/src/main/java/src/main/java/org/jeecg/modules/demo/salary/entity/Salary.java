package org.jeecg.modules.demo.salary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: salary
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("salary")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="salary对象", description="salary")
public class Salary implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**stime*/
	@Excel(name = "stime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "stime")
    private java.util.Date stime;
	/**sord*/
	@Excel(name = "sord", width = 15)
    @ApiModelProperty(value = "sord")
    private java.math.BigDecimal sord;
	/**srew*/
	@Excel(name = "srew", width = 15)
    @ApiModelProperty(value = "srew")
    private java.math.BigDecimal srew;
	/**stotal*/
	@Excel(name = "stotal", width = 15)
    @ApiModelProperty(value = "stotal")
    private java.math.BigDecimal stotal;
	/**did*/
	@Excel(name = "did", width = 15)
    @ApiModelProperty(value = "did")
    private java.lang.String did;
}
