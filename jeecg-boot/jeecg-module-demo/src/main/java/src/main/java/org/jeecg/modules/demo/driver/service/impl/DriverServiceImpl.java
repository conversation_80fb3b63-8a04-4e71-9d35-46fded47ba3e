package org.jeecg.modules.demo.driver.service.impl;

import org.jeecg.modules.demo.driver.entity.Driver;
import org.jeecg.modules.demo.driver.mapper.DriverMapper;
import org.jeecg.modules.demo.driver.service.IDriverService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: driver
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Service
public class DriverServiceImpl extends ServiceImpl<DriverMapper, Driver> implements IDriverService {

}
