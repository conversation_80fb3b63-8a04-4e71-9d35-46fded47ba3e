package org.jeecg.modules.demo.leetcodere.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: admin
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("admin")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="admin对象", description="admin")
public class Admin implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**aname*/
	@Excel(name = "aname", width = 15)
    @ApiModelProperty(value = "aname")
    private java.lang.String aname;
	/**asex*/
	@Excel(name = "asex", width = 15)
    @ApiModelProperty(value = "asex")
    private java.lang.String asex;
	/**atel*/
	@Excel(name = "atel", width = 15)
    @ApiModelProperty(value = "atel")
    private java.lang.String atel;
	/**apassword*/
	@Excel(name = "apassword", width = 15)
    @ApiModelProperty(value = "apassword")
    private java.lang.String apassword;
}
