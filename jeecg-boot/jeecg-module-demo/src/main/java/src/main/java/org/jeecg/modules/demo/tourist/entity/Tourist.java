package org.jeecg.modules.demo.tourist.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: tourist
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("tourist")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="tourist对象", description="tourist")
public class Tourist implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**tname*/
	@Excel(name = "tname", width = 15)
    @ApiModelProperty(value = "tname")
    private java.lang.String tname;
	/**tsex*/
	@Excel(name = "tsex", width = 15)
    @ApiModelProperty(value = "tsex")
    private java.lang.String tsex;
	/**ttel*/
	@Excel(name = "ttel", width = 15)
    @ApiModelProperty(value = "ttel")
    private java.lang.String ttel;
	/**tpassword*/
	@Excel(name = "tpassword", width = 15)
    @ApiModelProperty(value = "tpassword")
    private java.lang.String tpassword;
	/**vpre*/
	@Excel(name = "vpre", width = 15)
    @ApiModelProperty(value = "vpre")
    private java.lang.String vpre;
	/**dpre*/
	@Excel(name = "dpre", width = 15)
    @ApiModelProperty(value = "dpre")
    private java.lang.String dpre;
	/**lpre*/
	@Excel(name = "lpre", width = 15)
    @ApiModelProperty(value = "lpre")
    private java.lang.String lpre;
	/**ppre*/
	@Excel(name = "ppre", width = 15)
    @ApiModelProperty(value = "ppre")
    private java.lang.String ppre;
	/**cpre*/
	@Excel(name = "cpre", width = 15)
    @ApiModelProperty(value = "cpre")
    private java.lang.String cpre;
	/**spre*/
	@Excel(name = "spre", width = 15)
    @ApiModelProperty(value = "spre")
    private java.lang.String spre;
}
