package org.jeecg.modules.demo.maintenance.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.maintenance.entity.Maintenance;
import org.jeecg.modules.demo.maintenance.service.IMaintenanceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: maintenance
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="maintenance")
@RestController
@RequestMapping("/maintenance/maintenance")
@Slf4j
public class MaintenanceController extends JeecgController<Maintenance, IMaintenanceService> {
	@Autowired
	private IMaintenanceService maintenanceService;
	
	/**
	 * 分页列表查询
	 *
	 * @param maintenance
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "maintenance-分页列表查询")
	@ApiOperation(value="maintenance-分页列表查询", notes="maintenance-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Maintenance>> queryPageList(Maintenance maintenance,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Maintenance> queryWrapper = QueryGenerator.initQueryWrapper(maintenance, req.getParameterMap());
		Page<Maintenance> page = new Page<Maintenance>(pageNo, pageSize);
		IPage<Maintenance> pageList = maintenanceService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param maintenance
	 * @return
	 */
	@AutoLog(value = "maintenance-添加")
	@ApiOperation(value="maintenance-添加", notes="maintenance-添加")
	@RequiresPermissions("maintenance:maintenance:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Maintenance maintenance) {
		maintenanceService.save(maintenance);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param maintenance
	 * @return
	 */
	@AutoLog(value = "maintenance-编辑")
	@ApiOperation(value="maintenance-编辑", notes="maintenance-编辑")
	@RequiresPermissions("maintenance:maintenance:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Maintenance maintenance) {
		maintenanceService.updateById(maintenance);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "maintenance-通过id删除")
	@ApiOperation(value="maintenance-通过id删除", notes="maintenance-通过id删除")
	@RequiresPermissions("maintenance:maintenance:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		maintenanceService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "maintenance-批量删除")
	@ApiOperation(value="maintenance-批量删除", notes="maintenance-批量删除")
	@RequiresPermissions("maintenance:maintenance:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.maintenanceService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "maintenance-通过id查询")
	@ApiOperation(value="maintenance-通过id查询", notes="maintenance-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Maintenance> queryById(@RequestParam(name="id",required=true) String id) {
		Maintenance maintenance = maintenanceService.getById(id);
		if(maintenance==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(maintenance);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param maintenance
    */
    @RequiresPermissions("maintenance:maintenance:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Maintenance maintenance) {
        return super.exportXls(request, maintenance, Maintenance.class, "maintenance");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("maintenance:maintenance:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Maintenance.class);
    }

}
