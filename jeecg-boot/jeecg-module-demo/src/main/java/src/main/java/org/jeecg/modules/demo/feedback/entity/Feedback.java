package org.jeecg.modules.demo.feedback.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: feedback
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("feedback")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="feedback对象", description="feedback")
public class Feedback implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**devalu*/
	@Excel(name = "devalu", width = 15)
    @ApiModelProperty(value = "devalu")
    private java.lang.String devalu;
	/**fdgrade*/
	@Excel(name = "fdgrade", width = 15)
    @ApiModelProperty(value = "fdgrade")
    private java.math.BigDecimal fdgrade;
	/**revalu*/
	@Excel(name = "revalu", width = 15)
    @ApiModelProperty(value = "revalu")
    private java.lang.String revalu;
	/**frgrade*/
	@Excel(name = "frgrade", width = 15)
    @ApiModelProperty(value = "frgrade")
    private java.math.BigDecimal frgrade;
	/**sevalu*/
	@Excel(name = "sevalu", width = 15)
    @ApiModelProperty(value = "sevalu")
    private java.lang.String sevalu;
	/**fsgrade*/
	@Excel(name = "fsgrade", width = 15)
    @ApiModelProperty(value = "fsgrade")
    private java.math.BigDecimal fsgrade;
	/**ftime*/
	@Excel(name = "ftime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "ftime")
    private java.util.Date ftime;
	/**oid*/
	@Excel(name = "oid", width = 15)
    @ApiModelProperty(value = "oid")
    private java.lang.String oid;
}
