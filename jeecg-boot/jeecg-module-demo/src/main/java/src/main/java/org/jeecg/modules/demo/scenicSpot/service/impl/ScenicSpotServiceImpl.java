package org.jeecg.modules.demo.scenicSpot.service.impl;

import org.jeecg.modules.demo.scenicSpot.entity.ScenicSpot;
import org.jeecg.modules.demo.scenicSpot.mapper.ScenicSpotMapper;
import org.jeecg.modules.demo.scenicSpot.service.IScenicSpotService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: scenic_spot
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Service
public class ScenicSpotServiceImpl extends ServiceImpl<ScenicSpotMapper, ScenicSpot> implements IScenicSpotService {

}
