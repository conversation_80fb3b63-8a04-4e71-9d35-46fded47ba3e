import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'devalu',
    align:"center",
    dataIndex: 'devalu'
   },
   {
    title: 'fdgrade',
    align:"center",
    dataIndex: 'fdgrade'
   },
   {
    title: 'revalu',
    align:"center",
    dataIndex: 'revalu'
   },
   {
    title: 'frgrade',
    align:"center",
    dataIndex: 'frgrade'
   },
   {
    title: 'sevalu',
    align:"center",
    dataIndex: 'sevalu'
   },
   {
    title: 'fsgrade',
    align:"center",
    dataIndex: 'fsgrade'
   },
   {
    title: 'ftime',
    align:"center",
    dataIndex: 'ftime'
   },
   {
    title: 'oid',
    align:"center",
    dataIndex: 'oid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'devalu',
    field: 'devalu',
    component: 'InputTextArea',
  },
  {
    label: 'fdgrade',
    field: 'fdgrade',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入fdgrade!'},
          ];
     },
  },
  {
    label: 'revalu',
    field: 'revalu',
    component: 'InputTextArea',
  },
  {
    label: 'frgrade',
    field: 'frgrade',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入frgrade!'},
          ];
     },
  },
  {
    label: 'sevalu',
    field: 'sevalu',
    component: 'InputTextArea',
  },
  {
    label: 'fsgrade',
    field: 'fsgrade',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入fsgrade!'},
          ];
     },
  },
  {
    label: 'ftime',
    field: 'ftime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入ftime!'},
          ];
     },
  },
  {
    label: 'oid',
    field: 'oid',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入oid!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  devalu: {title: 'devalu',order: 0,view: 'textarea', type: 'string',},
  fdgrade: {title: 'fdgrade',order: 1,view: 'number', type: 'number',},
  revalu: {title: 'revalu',order: 2,view: 'textarea', type: 'string',},
  frgrade: {title: 'frgrade',order: 3,view: 'number', type: 'number',},
  sevalu: {title: 'sevalu',order: 4,view: 'textarea', type: 'string',},
  fsgrade: {title: 'fsgrade',order: 5,view: 'number', type: 'number',},
  ftime: {title: 'ftime',order: 6,view: 'datetime', type: 'string',},
  oid: {title: 'oid',order: 7,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}