package org.jeecg.modules.demo.leetcodere.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.leetcodere.entity.Admin;
import org.jeecg.modules.demo.leetcodere.service.IAdminService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: admin
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="admin")
@RestController
@RequestMapping("/leetcodere/admin")
@Slf4j
public class AdminController extends JeecgController<Admin, IAdminService> {
	@Autowired
	private IAdminService adminService;
	
	/**
	 * 分页列表查询
	 *
	 * @param admin
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "admin-分页列表查询")
	@ApiOperation(value="admin-分页列表查询", notes="admin-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Admin>> queryPageList(Admin admin,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Admin> queryWrapper = QueryGenerator.initQueryWrapper(admin, req.getParameterMap());
		Page<Admin> page = new Page<Admin>(pageNo, pageSize);
		IPage<Admin> pageList = adminService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param admin
	 * @return
	 */
	@AutoLog(value = "admin-添加")
	@ApiOperation(value="admin-添加", notes="admin-添加")
	@RequiresPermissions("leetcodere:admin:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Admin admin) {
		adminService.save(admin);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param admin
	 * @return
	 */
	@AutoLog(value = "admin-编辑")
	@ApiOperation(value="admin-编辑", notes="admin-编辑")
	@RequiresPermissions("leetcodere:admin:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Admin admin) {
		adminService.updateById(admin);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "admin-通过id删除")
	@ApiOperation(value="admin-通过id删除", notes="admin-通过id删除")
	@RequiresPermissions("leetcodere:admin:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		adminService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "admin-批量删除")
	@ApiOperation(value="admin-批量删除", notes="admin-批量删除")
	@RequiresPermissions("leetcodere:admin:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.adminService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "admin-通过id查询")
	@ApiOperation(value="admin-通过id查询", notes="admin-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Admin> queryById(@RequestParam(name="id",required=true) String id) {
		Admin admin = adminService.getById(id);
		if(admin==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(admin);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param admin
    */
    @RequiresPermissions("leetcodere:admin:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Admin admin) {
        return super.exportXls(request, admin, Admin.class, "admin");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("leetcodere:admin:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Admin.class);
    }

}
