package org.jeecg.modules.demo.vehicle.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: vehicle
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("vehicle")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="vehicle对象", description="vehicle")
public class Vehicle implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**vtype*/
	@Excel(name = "vtype", width = 15)
    @ApiModelProperty(value = "vtype")
    private java.lang.String vtype;
	/**vnum*/
	@Excel(name = "vnum", width = 15)
    @ApiModelProperty(value = "vnum")
    private java.lang.String vnum;
	/**vseats*/
	@Excel(name = "vseats", width = 15)
    @ApiModelProperty(value = "vseats")
    private java.lang.Integer vseats;
	/**vbuy*/
	@Excel(name = "vbuy", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "vbuy")
    private java.util.Date vbuy;
	/**vstatus*/
	@Excel(name = "vstatus", width = 15)
    @ApiModelProperty(value = "vstatus")
    private java.lang.String vstatus;
}
