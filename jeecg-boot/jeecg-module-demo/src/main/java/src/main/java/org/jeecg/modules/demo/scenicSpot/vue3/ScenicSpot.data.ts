import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'sname',
    align:"center",
    dataIndex: 'sname'
   },
   {
    title: 'sloc',
    align:"center",
    dataIndex: 'sloc'
   },
   {
    title: 'otime',
    align:"center",
    dataIndex: 'otime'
   },
   {
    title: 'sdes',
    align:"center",
    dataIndex: 'sdes'
   },
   {
    title: 'sgrade',
    align:"center",
    dataIndex: 'sgrade'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'sname',
    field: 'sname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入sname!'},
          ];
     },
  },
  {
    label: 'sloc',
    field: 'sloc',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入sloc!'},
          ];
     },
  },
  {
    label: 'otime',
    field: 'otime',
    component: 'Input',
  },
  {
    label: 'sdes',
    field: 'sdes',
    component: 'InputTextArea',
  },
  {
    label: 'sgrade',
    field: 'sgrade',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sname: {title: 'sname',order: 0,view: 'text', type: 'string',},
  sloc: {title: 'sloc',order: 1,view: 'text', type: 'string',},
  otime: {title: 'otime',order: 2,view: 'text', type: 'string',},
  sdes: {title: 'sdes',order: 3,view: 'textarea', type: 'string',},
  sgrade: {title: 'sgrade',order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}