package org.jeecg.modules.demo.scenicSpot.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.scenicSpot.entity.ScenicSpot;
import org.jeecg.modules.demo.scenicSpot.service.IScenicSpotService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: scenic_spot
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="scenic_spot")
@RestController
@RequestMapping("/scenicSpot/scenicSpot")
@Slf4j
public class ScenicSpotController extends JeecgController<ScenicSpot, IScenicSpotService> {
	@Autowired
	private IScenicSpotService scenicSpotService;
	
	/**
	 * 分页列表查询
	 *
	 * @param scenicSpot
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "scenic_spot-分页列表查询")
	@ApiOperation(value="scenic_spot-分页列表查询", notes="scenic_spot-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ScenicSpot>> queryPageList(ScenicSpot scenicSpot,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ScenicSpot> queryWrapper = QueryGenerator.initQueryWrapper(scenicSpot, req.getParameterMap());
		Page<ScenicSpot> page = new Page<ScenicSpot>(pageNo, pageSize);
		IPage<ScenicSpot> pageList = scenicSpotService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param scenicSpot
	 * @return
	 */
	@AutoLog(value = "scenic_spot-添加")
	@ApiOperation(value="scenic_spot-添加", notes="scenic_spot-添加")
	@RequiresPermissions("scenicSpot:scenic_spot:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ScenicSpot scenicSpot) {
		scenicSpotService.save(scenicSpot);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param scenicSpot
	 * @return
	 */
	@AutoLog(value = "scenic_spot-编辑")
	@ApiOperation(value="scenic_spot-编辑", notes="scenic_spot-编辑")
	@RequiresPermissions("scenicSpot:scenic_spot:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ScenicSpot scenicSpot) {
		scenicSpotService.updateById(scenicSpot);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "scenic_spot-通过id删除")
	@ApiOperation(value="scenic_spot-通过id删除", notes="scenic_spot-通过id删除")
	@RequiresPermissions("scenicSpot:scenic_spot:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		scenicSpotService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "scenic_spot-批量删除")
	@ApiOperation(value="scenic_spot-批量删除", notes="scenic_spot-批量删除")
	@RequiresPermissions("scenicSpot:scenic_spot:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.scenicSpotService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "scenic_spot-通过id查询")
	@ApiOperation(value="scenic_spot-通过id查询", notes="scenic_spot-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ScenicSpot> queryById(@RequestParam(name="id",required=true) String id) {
		ScenicSpot scenicSpot = scenicSpotService.getById(id);
		if(scenicSpot==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(scenicSpot);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param scenicSpot
    */
    @RequiresPermissions("scenicSpot:scenic_spot:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ScenicSpot scenicSpot) {
        return super.exportXls(request, scenicSpot, ScenicSpot.class, "scenic_spot");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("scenicSpot:scenic_spot:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScenicSpot.class);
    }

}
