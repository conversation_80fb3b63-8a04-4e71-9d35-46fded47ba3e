import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'mtype',
    align:"center",
    dataIndex: 'mtype'
   },
   {
    title: 'mcon',
    align:"center",
    dataIndex: 'mcon'
   },
   {
    title: 'mtim',
    align:"center",
    dataIndex: 'mtim'
   },
   {
    title: 'mprice',
    align:"center",
    dataIndex: 'mprice'
   },
   {
    title: 'vid',
    align:"center",
    dataIndex: 'vid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'mtype',
    field: 'mtype',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入mtype!'},
          ];
     },
  },
  {
    label: 'mcon',
    field: 'mcon',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入mcon!'},
          ];
     },
  },
  {
    label: 'mtim',
    field: 'mtim',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入mtim!'},
          ];
     },
  },
  {
    label: 'mprice',
    field: 'mprice',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入mprice!'},
          ];
     },
  },
  {
    label: 'vid',
    field: 'vid',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入vid!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  mtype: {title: 'mtype',order: 0,view: 'text', type: 'string',},
  mcon: {title: 'mcon',order: 1,view: 'textarea', type: 'string',},
  mtim: {title: 'mtim',order: 2,view: 'datetime', type: 'string',},
  mprice: {title: 'mprice',order: 3,view: 'number', type: 'number',},
  vid: {title: 'vid',order: 4,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}