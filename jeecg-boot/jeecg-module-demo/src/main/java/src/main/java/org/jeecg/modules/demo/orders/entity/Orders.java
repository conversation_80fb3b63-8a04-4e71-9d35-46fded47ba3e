package org.jeecg.modules.demo.orders.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: orders
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("orders")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="orders对象", description="orders")
public class Orders implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**ostatus*/
	@Excel(name = "ostatus", width = 15)
    @ApiModelProperty(value = "ostatus")
    private java.lang.String ostatus;
	/**oprice*/
	@Excel(name = "oprice", width = 15)
    @ApiModelProperty(value = "oprice")
    private java.math.BigDecimal oprice;
	/**onum*/
	@Excel(name = "onum", width = 15)
    @ApiModelProperty(value = "onum")
    private java.lang.Integer onum;
	/**btime*/
	@Excel(name = "btime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "btime")
    private java.util.Date btime;
	/**etime*/
	@Excel(name = "etime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "etime")
    private java.util.Date etime;
	/**tid*/
	@Excel(name = "tid", width = 15)
    @ApiModelProperty(value = "tid")
    private java.lang.String tid;
	/**did*/
	@Excel(name = "did", width = 15)
    @ApiModelProperty(value = "did")
    private java.lang.String did;
	/**vid*/
	@Excel(name = "vid", width = 15)
    @ApiModelProperty(value = "vid")
    private java.lang.String vid;
	/**rid*/
	@Excel(name = "rid", width = 15)
    @ApiModelProperty(value = "rid")
    private java.lang.String rid;
}
