package org.jeecg.modules.demo.route.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: route
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("route")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="route对象", description="route")
public class Route implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**rname*/
	@Excel(name = "rname", width = 15)
    @ApiModelProperty(value = "rname")
    private java.lang.String rname;
	/**rtime*/
	@Excel(name = "rtime", width = 15)
    @ApiModelProperty(value = "rtime")
    private java.lang.String rtime;
	/**rgrade*/
	@Excel(name = "rgrade", width = 15)
    @ApiModelProperty(value = "rgrade")
    private java.math.BigDecimal rgrade;
}
