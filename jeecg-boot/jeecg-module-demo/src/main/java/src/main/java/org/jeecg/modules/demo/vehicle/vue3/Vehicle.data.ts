import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'vtype',
    align:"center",
    dataIndex: 'vtype'
   },
   {
    title: 'vnum',
    align:"center",
    dataIndex: 'vnum'
   },
   {
    title: 'vseats',
    align:"center",
    dataIndex: 'vseats'
   },
   {
    title: 'vbuy',
    align:"center",
    dataIndex: 'vbuy',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'vstatus',
    align:"center",
    dataIndex: 'vstatus'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'vtype',
    field: 'vtype',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入vtype!'},
          ];
     },
  },
  {
    label: 'vnum',
    field: 'vnum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入vnum!'},
          ];
     },
  },
  {
    label: 'vseats',
    field: 'vseats',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入vseats!'},
          ];
     },
  },
  {
    label: 'vbuy',
    field: 'vbuy',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入vbuy!'},
          ];
     },
  },
  {
    label: 'vstatus',
    field: 'vstatus',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  vtype: {title: 'vtype',order: 0,view: 'text', type: 'string',},
  vnum: {title: 'vnum',order: 1,view: 'text', type: 'string',},
  vseats: {title: 'vseats',order: 2,view: 'number', type: 'number',},
  vbuy: {title: 'vbuy',order: 3,view: 'date', type: 'string',},
  vstatus: {title: 'vstatus',order: 4,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}