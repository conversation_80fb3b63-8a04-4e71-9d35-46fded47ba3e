package org.jeecg.modules.demo.vehicle.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.vehicle.entity.Vehicle;
import org.jeecg.modules.demo.vehicle.service.IVehicleService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: vehicle
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="vehicle")
@RestController
@RequestMapping("/vehicle/vehicle")
@Slf4j
public class VehicleController extends JeecgController<Vehicle, IVehicleService> {
	@Autowired
	private IVehicleService vehicleService;
	
	/**
	 * 分页列表查询
	 *
	 * @param vehicle
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "vehicle-分页列表查询")
	@ApiOperation(value="vehicle-分页列表查询", notes="vehicle-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Vehicle>> queryPageList(Vehicle vehicle,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Vehicle> queryWrapper = QueryGenerator.initQueryWrapper(vehicle, req.getParameterMap());
		Page<Vehicle> page = new Page<Vehicle>(pageNo, pageSize);
		IPage<Vehicle> pageList = vehicleService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param vehicle
	 * @return
	 */
	@AutoLog(value = "vehicle-添加")
	@ApiOperation(value="vehicle-添加", notes="vehicle-添加")
	@RequiresPermissions("vehicle:vehicle:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Vehicle vehicle) {
		vehicleService.save(vehicle);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param vehicle
	 * @return
	 */
	@AutoLog(value = "vehicle-编辑")
	@ApiOperation(value="vehicle-编辑", notes="vehicle-编辑")
	@RequiresPermissions("vehicle:vehicle:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Vehicle vehicle) {
		vehicleService.updateById(vehicle);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "vehicle-通过id删除")
	@ApiOperation(value="vehicle-通过id删除", notes="vehicle-通过id删除")
	@RequiresPermissions("vehicle:vehicle:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		vehicleService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "vehicle-批量删除")
	@ApiOperation(value="vehicle-批量删除", notes="vehicle-批量删除")
	@RequiresPermissions("vehicle:vehicle:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.vehicleService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "vehicle-通过id查询")
	@ApiOperation(value="vehicle-通过id查询", notes="vehicle-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Vehicle> queryById(@RequestParam(name="id",required=true) String id) {
		Vehicle vehicle = vehicleService.getById(id);
		if(vehicle==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(vehicle);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param vehicle
    */
    @RequiresPermissions("vehicle:vehicle:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Vehicle vehicle) {
        return super.exportXls(request, vehicle, Vehicle.class, "vehicle");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("vehicle:vehicle:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Vehicle.class);
    }

}
