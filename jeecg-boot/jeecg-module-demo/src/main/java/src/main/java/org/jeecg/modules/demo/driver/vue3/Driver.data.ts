import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'dname',
    align:"center",
    dataIndex: 'dname'
   },
   {
    title: 'dsex',
    align:"center",
    dataIndex: 'dsex'
   },
   {
    title: 'dtel',
    align:"center",
    dataIndex: 'dtel'
   },
   {
    title: 'dlic',
    align:"center",
    dataIndex: 'dlic'
   },
   {
    title: 'dexp',
    align:"center",
    dataIndex: 'dexp'
   },
   {
    title: 'dgrade',
    align:"center",
    dataIndex: 'dgrade'
   },
   {
    title: 'dstatus',
    align:"center",
    dataIndex: 'dstatus'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'dname',
    field: 'dname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入dname!'},
          ];
     },
  },
  {
    label: 'dsex',
    field: 'dsex',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入dsex!'},
          ];
     },
  },
  {
    label: 'dtel',
    field: 'dtel',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入dtel!'},
          ];
     },
  },
  {
    label: 'dlic',
    field: 'dlic',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入dlic!'},
          ];
     },
  },
  {
    label: 'dexp',
    field: 'dexp',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入dexp!'},
          ];
     },
  },
  {
    label: 'dgrade',
    field: 'dgrade',
    component: 'InputNumber',
  },
  {
    label: 'dstatus',
    field: 'dstatus',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  dname: {title: 'dname',order: 0,view: 'text', type: 'string',},
  dsex: {title: 'dsex',order: 1,view: 'text', type: 'string',},
  dtel: {title: 'dtel',order: 2,view: 'text', type: 'string',},
  dlic: {title: 'dlic',order: 3,view: 'text', type: 'string',},
  dexp: {title: 'dexp',order: 4,view: 'number', type: 'number',},
  dgrade: {title: 'dgrade',order: 5,view: 'number', type: 'number',},
  dstatus: {title: 'dstatus',order: 6,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}