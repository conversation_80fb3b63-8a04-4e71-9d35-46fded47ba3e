package org.jeecg.modules.demo.routeSpot.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.routeSpot.entity.RouteSpot;
import org.jeecg.modules.demo.routeSpot.service.IRouteSpotService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: route_spot
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="route_spot")
@RestController
@RequestMapping("/routeSpot/routeSpot")
@Slf4j
public class RouteSpotController extends JeecgController<RouteSpot, IRouteSpotService> {
	@Autowired
	private IRouteSpotService routeSpotService;
	
	/**
	 * 分页列表查询
	 *
	 * @param routeSpot
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "route_spot-分页列表查询")
	@ApiOperation(value="route_spot-分页列表查询", notes="route_spot-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RouteSpot>> queryPageList(RouteSpot routeSpot,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<RouteSpot> queryWrapper = QueryGenerator.initQueryWrapper(routeSpot, req.getParameterMap());
		Page<RouteSpot> page = new Page<RouteSpot>(pageNo, pageSize);
		IPage<RouteSpot> pageList = routeSpotService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param routeSpot
	 * @return
	 */
	@AutoLog(value = "route_spot-添加")
	@ApiOperation(value="route_spot-添加", notes="route_spot-添加")
	@RequiresPermissions("routeSpot:route_spot:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RouteSpot routeSpot) {
		routeSpotService.save(routeSpot);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param routeSpot
	 * @return
	 */
	@AutoLog(value = "route_spot-编辑")
	@ApiOperation(value="route_spot-编辑", notes="route_spot-编辑")
	@RequiresPermissions("routeSpot:route_spot:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RouteSpot routeSpot) {
		routeSpotService.updateById(routeSpot);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "route_spot-通过id删除")
	@ApiOperation(value="route_spot-通过id删除", notes="route_spot-通过id删除")
	@RequiresPermissions("routeSpot:route_spot:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		routeSpotService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "route_spot-批量删除")
	@ApiOperation(value="route_spot-批量删除", notes="route_spot-批量删除")
	@RequiresPermissions("routeSpot:route_spot:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.routeSpotService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "route_spot-通过id查询")
	@ApiOperation(value="route_spot-通过id查询", notes="route_spot-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RouteSpot> queryById(@RequestParam(name="id",required=true) String id) {
		RouteSpot routeSpot = routeSpotService.getById(id);
		if(routeSpot==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(routeSpot);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param routeSpot
    */
    @RequiresPermissions("routeSpot:route_spot:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RouteSpot routeSpot) {
        return super.exportXls(request, routeSpot, RouteSpot.class, "route_spot");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("routeSpot:route_spot:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RouteSpot.class);
    }

}
