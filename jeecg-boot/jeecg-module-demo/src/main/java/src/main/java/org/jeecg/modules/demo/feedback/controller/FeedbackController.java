package org.jeecg.modules.demo.feedback.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.feedback.entity.Feedback;
import org.jeecg.modules.demo.feedback.service.IFeedbackService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: feedback
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="feedback")
@RestController
@RequestMapping("/feedback/feedback")
@Slf4j
public class FeedbackController extends JeecgController<Feedback, IFeedbackService> {
	@Autowired
	private IFeedbackService feedbackService;
	
	/**
	 * 分页列表查询
	 *
	 * @param feedback
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "feedback-分页列表查询")
	@ApiOperation(value="feedback-分页列表查询", notes="feedback-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Feedback>> queryPageList(Feedback feedback,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Feedback> queryWrapper = QueryGenerator.initQueryWrapper(feedback, req.getParameterMap());
		Page<Feedback> page = new Page<Feedback>(pageNo, pageSize);
		IPage<Feedback> pageList = feedbackService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param feedback
	 * @return
	 */
	@AutoLog(value = "feedback-添加")
	@ApiOperation(value="feedback-添加", notes="feedback-添加")
	@RequiresPermissions("feedback:feedback:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Feedback feedback) {
		feedbackService.save(feedback);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param feedback
	 * @return
	 */
	@AutoLog(value = "feedback-编辑")
	@ApiOperation(value="feedback-编辑", notes="feedback-编辑")
	@RequiresPermissions("feedback:feedback:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Feedback feedback) {
		feedbackService.updateById(feedback);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "feedback-通过id删除")
	@ApiOperation(value="feedback-通过id删除", notes="feedback-通过id删除")
	@RequiresPermissions("feedback:feedback:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		feedbackService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "feedback-批量删除")
	@ApiOperation(value="feedback-批量删除", notes="feedback-批量删除")
	@RequiresPermissions("feedback:feedback:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.feedbackService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "feedback-通过id查询")
	@ApiOperation(value="feedback-通过id查询", notes="feedback-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Feedback> queryById(@RequestParam(name="id",required=true) String id) {
		Feedback feedback = feedbackService.getById(id);
		if(feedback==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(feedback);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param feedback
    */
    @RequiresPermissions("feedback:feedback:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Feedback feedback) {
        return super.exportXls(request, feedback, Feedback.class, "feedback");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("feedback:feedback:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Feedback.class);
    }

}
