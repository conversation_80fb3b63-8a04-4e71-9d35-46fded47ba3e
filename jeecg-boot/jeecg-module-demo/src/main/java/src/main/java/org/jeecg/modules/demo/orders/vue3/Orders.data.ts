import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'ostatus',
    align:"center",
    dataIndex: 'ostatus'
   },
   {
    title: 'oprice',
    align:"center",
    dataIndex: 'oprice'
   },
   {
    title: 'onum',
    align:"center",
    dataIndex: 'onum'
   },
   {
    title: 'btime',
    align:"center",
    dataIndex: 'btime'
   },
   {
    title: 'etime',
    align:"center",
    dataIndex: 'etime'
   },
   {
    title: 'tid',
    align:"center",
    dataIndex: 'tid'
   },
   {
    title: 'did',
    align:"center",
    dataIndex: 'did'
   },
   {
    title: 'vid',
    align:"center",
    dataIndex: 'vid'
   },
   {
    title: 'rid',
    align:"center",
    dataIndex: 'rid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'ostatus',
    field: 'ostatus',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入ostatus!'},
          ];
     },
  },
  {
    label: 'oprice',
    field: 'oprice',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入oprice!'},
          ];
     },
  },
  {
    label: 'onum',
    field: 'onum',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入onum!'},
          ];
     },
  },
  {
    label: 'btime',
    field: 'btime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入btime!'},
          ];
     },
  },
  {
    label: 'etime',
    field: 'etime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: 'tid',
    field: 'tid',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入tid!'},
          ];
     },
  },
  {
    label: 'did',
    field: 'did',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入did!'},
          ];
     },
  },
  {
    label: 'vid',
    field: 'vid',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入vid!'},
          ];
     },
  },
  {
    label: 'rid',
    field: 'rid',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入rid!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  ostatus: {title: 'ostatus',order: 0,view: 'text', type: 'string',},
  oprice: {title: 'oprice',order: 1,view: 'number', type: 'number',},
  onum: {title: 'onum',order: 2,view: 'number', type: 'number',},
  btime: {title: 'btime',order: 3,view: 'datetime', type: 'string',},
  etime: {title: 'etime',order: 4,view: 'datetime', type: 'string',},
  tid: {title: 'tid',order: 5,view: 'text', type: 'string',},
  did: {title: 'did',order: 6,view: 'text', type: 'string',},
  vid: {title: 'vid',order: 7,view: 'text', type: 'string',},
  rid: {title: 'rid',order: 8,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}