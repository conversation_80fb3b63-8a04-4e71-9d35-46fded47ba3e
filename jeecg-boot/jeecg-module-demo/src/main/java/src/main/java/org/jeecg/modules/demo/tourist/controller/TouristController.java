package org.jeecg.modules.demo.tourist.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.tourist.entity.Tourist;
import org.jeecg.modules.demo.tourist.service.ITouristService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: tourist
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="tourist")
@RestController
@RequestMapping("/tourist/tourist")
@Slf4j
public class TouristController extends JeecgController<Tourist, ITouristService> {
	@Autowired
	private ITouristService touristService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tourist
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "tourist-分页列表查询")
	@ApiOperation(value="tourist-分页列表查询", notes="tourist-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Tourist>> queryPageList(Tourist tourist,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Tourist> queryWrapper = QueryGenerator.initQueryWrapper(tourist, req.getParameterMap());
		Page<Tourist> page = new Page<Tourist>(pageNo, pageSize);
		IPage<Tourist> pageList = touristService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tourist
	 * @return
	 */
	@AutoLog(value = "tourist-添加")
	@ApiOperation(value="tourist-添加", notes="tourist-添加")
	@RequiresPermissions("tourist:tourist:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Tourist tourist) {
		touristService.save(tourist);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tourist
	 * @return
	 */
	@AutoLog(value = "tourist-编辑")
	@ApiOperation(value="tourist-编辑", notes="tourist-编辑")
	@RequiresPermissions("tourist:tourist:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Tourist tourist) {
		touristService.updateById(tourist);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "tourist-通过id删除")
	@ApiOperation(value="tourist-通过id删除", notes="tourist-通过id删除")
	@RequiresPermissions("tourist:tourist:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		touristService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "tourist-批量删除")
	@ApiOperation(value="tourist-批量删除", notes="tourist-批量删除")
	@RequiresPermissions("tourist:tourist:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.touristService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "tourist-通过id查询")
	@ApiOperation(value="tourist-通过id查询", notes="tourist-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Tourist> queryById(@RequestParam(name="id",required=true) String id) {
		Tourist tourist = touristService.getById(id);
		if(tourist==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tourist);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param tourist
    */
    @RequiresPermissions("tourist:tourist:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Tourist tourist) {
        return super.exportXls(request, tourist, Tourist.class, "tourist");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("tourist:tourist:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Tourist.class);
    }

}
