import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'tname',
    align:"center",
    dataIndex: 'tname'
   },
   {
    title: 'tsex',
    align:"center",
    dataIndex: 'tsex'
   },
   {
    title: 'ttel',
    align:"center",
    dataIndex: 'ttel'
   },
   {
    title: 'tpassword',
    align:"center",
    dataIndex: 'tpassword'
   },
   {
    title: 'vpre',
    align:"center",
    dataIndex: 'vpre'
   },
   {
    title: 'dpre',
    align:"center",
    dataIndex: 'dpre'
   },
   {
    title: 'lpre',
    align:"center",
    dataIndex: 'lpre'
   },
   {
    title: 'ppre',
    align:"center",
    dataIndex: 'ppre'
   },
   {
    title: 'cpre',
    align:"center",
    dataIndex: 'cpre'
   },
   {
    title: 'spre',
    align:"center",
    dataIndex: 'spre'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'tname',
    field: 'tname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入tname!'},
          ];
     },
  },
  {
    label: 'tsex',
    field: 'tsex',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入tsex!'},
          ];
     },
  },
  {
    label: 'ttel',
    field: 'ttel',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入ttel!'},
          ];
     },
  },
  {
    label: 'tpassword',
    field: 'tpassword',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入tpassword!'},
          ];
     },
  },
  {
    label: 'vpre',
    field: 'vpre',
    component: 'Input',
  },
  {
    label: 'dpre',
    field: 'dpre',
    component: 'Input',
  },
  {
    label: 'lpre',
    field: 'lpre',
    component: 'Input',
  },
  {
    label: 'ppre',
    field: 'ppre',
    component: 'Input',
  },
  {
    label: 'cpre',
    field: 'cpre',
    component: 'Input',
  },
  {
    label: 'spre',
    field: 'spre',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  tname: {title: 'tname',order: 0,view: 'text', type: 'string',},
  tsex: {title: 'tsex',order: 1,view: 'text', type: 'string',},
  ttel: {title: 'ttel',order: 2,view: 'text', type: 'string',},
  tpassword: {title: 'tpassword',order: 3,view: 'text', type: 'string',},
  vpre: {title: 'vpre',order: 4,view: 'text', type: 'string',},
  dpre: {title: 'dpre',order: 5,view: 'text', type: 'string',},
  lpre: {title: 'lpre',order: 6,view: 'text', type: 'string',},
  ppre: {title: 'ppre',order: 7,view: 'text', type: 'string',},
  cpre: {title: 'cpre',order: 8,view: 'text', type: 'string',},
  spre: {title: 'spre',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}