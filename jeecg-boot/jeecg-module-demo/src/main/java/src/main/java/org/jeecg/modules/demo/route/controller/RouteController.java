package org.jeecg.modules.demo.route.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.route.entity.Route;
import org.jeecg.modules.demo.route.service.IRouteService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: route
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="route")
@RestController
@RequestMapping("/route/route")
@Slf4j
public class RouteController extends JeecgController<Route, IRouteService> {
	@Autowired
	private IRouteService routeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param route
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "route-分页列表查询")
	@ApiOperation(value="route-分页列表查询", notes="route-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Route>> queryPageList(Route route,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Route> queryWrapper = QueryGenerator.initQueryWrapper(route, req.getParameterMap());
		Page<Route> page = new Page<Route>(pageNo, pageSize);
		IPage<Route> pageList = routeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param route
	 * @return
	 */
	@AutoLog(value = "route-添加")
	@ApiOperation(value="route-添加", notes="route-添加")
	@RequiresPermissions("route:route:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Route route) {
		routeService.save(route);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param route
	 * @return
	 */
	@AutoLog(value = "route-编辑")
	@ApiOperation(value="route-编辑", notes="route-编辑")
	@RequiresPermissions("route:route:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Route route) {
		routeService.updateById(route);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "route-通过id删除")
	@ApiOperation(value="route-通过id删除", notes="route-通过id删除")
	@RequiresPermissions("route:route:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		routeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "route-批量删除")
	@ApiOperation(value="route-批量删除", notes="route-批量删除")
	@RequiresPermissions("route:route:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.routeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "route-通过id查询")
	@ApiOperation(value="route-通过id查询", notes="route-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Route> queryById(@RequestParam(name="id",required=true) String id) {
		Route route = routeService.getById(id);
		if(route==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(route);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param route
    */
    @RequiresPermissions("route:route:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Route route) {
        return super.exportXls(request, route, Route.class, "route");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("route:route:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Route.class);
    }

}
