package org.jeecg.modules.demo.scenicSpot.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: scenic_spot
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("scenic_spot")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="scenic_spot对象", description="scenic_spot")
public class ScenicSpot implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**sname*/
	@Excel(name = "sname", width = 15)
    @ApiModelProperty(value = "sname")
    private java.lang.String sname;
	/**sloc*/
	@Excel(name = "sloc", width = 15)
    @ApiModelProperty(value = "sloc")
    private java.lang.String sloc;
	/**otime*/
	@Excel(name = "otime", width = 15)
    @ApiModelProperty(value = "otime")
    private java.lang.String otime;
	/**sdes*/
	@Excel(name = "sdes", width = 15)
    @ApiModelProperty(value = "sdes")
    private java.lang.String sdes;
	/**sgrade*/
	@Excel(name = "sgrade", width = 15)
    @ApiModelProperty(value = "sgrade")
    private java.math.BigDecimal sgrade;
}
