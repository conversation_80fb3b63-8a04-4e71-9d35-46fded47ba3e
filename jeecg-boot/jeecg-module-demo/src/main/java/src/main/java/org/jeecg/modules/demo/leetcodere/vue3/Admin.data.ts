import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'aname',
    align:"center",
    dataIndex: 'aname'
   },
   {
    title: 'asex',
    align:"center",
    dataIndex: 'asex'
   },
   {
    title: 'atel',
    align:"center",
    dataIndex: 'atel'
   },
   {
    title: 'apassword',
    align:"center",
    dataIndex: 'apassword'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'aname',
    field: 'aname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入aname!'},
          ];
     },
  },
  {
    label: 'asex',
    field: 'asex',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入asex!'},
          ];
     },
  },
  {
    label: 'atel',
    field: 'atel',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入atel!'},
          ];
     },
  },
  {
    label: 'apassword',
    field: 'apassword',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入apassword!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  aname: {title: 'aname',order: 0,view: 'text', type: 'string',},
  asex: {title: 'asex',order: 1,view: 'text', type: 'string',},
  atel: {title: 'atel',order: 2,view: 'text', type: 'string',},
  apassword: {title: 'apassword',order: 3,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}