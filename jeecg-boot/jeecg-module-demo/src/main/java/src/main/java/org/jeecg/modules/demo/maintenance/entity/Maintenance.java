package org.jeecg.modules.demo.maintenance.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: maintenance
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("maintenance")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="maintenance对象", description="maintenance")
public class Maintenance implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**mtype*/
	@Excel(name = "mtype", width = 15)
    @ApiModelProperty(value = "mtype")
    private java.lang.String mtype;
	/**mcon*/
	@Excel(name = "mcon", width = 15)
    @ApiModelProperty(value = "mcon")
    private java.lang.String mcon;
	/**mtim*/
	@Excel(name = "mtim", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "mtim")
    private java.util.Date mtim;
	/**mprice*/
	@Excel(name = "mprice", width = 15)
    @ApiModelProperty(value = "mprice")
    private java.math.BigDecimal mprice;
	/**vid*/
	@Excel(name = "vid", width = 15)
    @ApiModelProperty(value = "vid")
    private java.lang.String vid;
}
