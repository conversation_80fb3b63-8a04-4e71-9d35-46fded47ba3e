package org.jeecg.modules.demo.driverTraining.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.driverTraining.entity.DriverTraining;
import org.jeecg.modules.demo.driverTraining.service.IDriverTrainingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: driver_training
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Api(tags="driver_training")
@RestController
@RequestMapping("/driverTraining/driverTraining")
@Slf4j
public class DriverTrainingController extends JeecgController<DriverTraining, IDriverTrainingService> {
	@Autowired
	private IDriverTrainingService driverTrainingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param driverTraining
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "driver_training-分页列表查询")
	@ApiOperation(value="driver_training-分页列表查询", notes="driver_training-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DriverTraining>> queryPageList(DriverTraining driverTraining,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<DriverTraining> queryWrapper = QueryGenerator.initQueryWrapper(driverTraining, req.getParameterMap());
		Page<DriverTraining> page = new Page<DriverTraining>(pageNo, pageSize);
		IPage<DriverTraining> pageList = driverTrainingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param driverTraining
	 * @return
	 */
	@AutoLog(value = "driver_training-添加")
	@ApiOperation(value="driver_training-添加", notes="driver_training-添加")
	@RequiresPermissions("driverTraining:driver_training:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DriverTraining driverTraining) {
		driverTrainingService.save(driverTraining);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param driverTraining
	 * @return
	 */
	@AutoLog(value = "driver_training-编辑")
	@ApiOperation(value="driver_training-编辑", notes="driver_training-编辑")
	@RequiresPermissions("driverTraining:driver_training:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DriverTraining driverTraining) {
		driverTrainingService.updateById(driverTraining);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "driver_training-通过id删除")
	@ApiOperation(value="driver_training-通过id删除", notes="driver_training-通过id删除")
	@RequiresPermissions("driverTraining:driver_training:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		driverTrainingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "driver_training-批量删除")
	@ApiOperation(value="driver_training-批量删除", notes="driver_training-批量删除")
	@RequiresPermissions("driverTraining:driver_training:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.driverTrainingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "driver_training-通过id查询")
	@ApiOperation(value="driver_training-通过id查询", notes="driver_training-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DriverTraining> queryById(@RequestParam(name="id",required=true) String id) {
		DriverTraining driverTraining = driverTrainingService.getById(id);
		if(driverTraining==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(driverTraining);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param driverTraining
    */
    @RequiresPermissions("driverTraining:driver_training:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DriverTraining driverTraining) {
        return super.exportXls(request, driverTraining, DriverTraining.class, "driver_training");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("driverTraining:driver_training:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DriverTraining.class);
    }

}
