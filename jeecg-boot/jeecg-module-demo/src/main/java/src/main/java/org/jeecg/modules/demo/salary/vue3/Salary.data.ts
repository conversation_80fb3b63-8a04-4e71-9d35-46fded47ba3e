import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'stime',
    align:"center",
    dataIndex: 'stime'
   },
   {
    title: 'sord',
    align:"center",
    dataIndex: 'sord'
   },
   {
    title: 'srew',
    align:"center",
    dataIndex: 'srew'
   },
   {
    title: 'stotal',
    align:"center",
    dataIndex: 'stotal'
   },
   {
    title: 'did',
    align:"center",
    dataIndex: 'did'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'stime',
    field: 'stime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入stime!'},
          ];
     },
  },
  {
    label: 'sord',
    field: 'sord',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入sord!'},
          ];
     },
  },
  {
    label: 'srew',
    field: 'srew',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入srew!'},
          ];
     },
  },
  {
    label: 'stotal',
    field: 'stotal',
    component: 'InputNumber',
  },
  {
    label: 'did',
    field: 'did',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入did!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  stime: {title: 'stime',order: 0,view: 'datetime', type: 'string',},
  sord: {title: 'sord',order: 1,view: 'number', type: 'number',},
  srew: {title: 'srew',order: 2,view: 'number', type: 'number',},
  stotal: {title: 'stotal',order: 3,view: 'number', type: 'number',},
  did: {title: 'did',order: 4,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}