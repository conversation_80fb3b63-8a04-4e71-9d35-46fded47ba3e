package org.jeecg.modules.demo.feedback.service.impl;

import org.jeecg.modules.demo.feedback.entity.Feedback;
import org.jeecg.modules.demo.feedback.mapper.FeedbackMapper;
import org.jeecg.modules.demo.feedback.service.IFeedbackService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: feedback
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Service
public class FeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements IFeedbackService {

}
