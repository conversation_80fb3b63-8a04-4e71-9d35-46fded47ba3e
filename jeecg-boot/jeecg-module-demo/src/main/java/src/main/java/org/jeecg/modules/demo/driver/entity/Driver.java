package org.jeecg.modules.demo.driver.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: driver
 * @Author: jeecg-boot
 * @Date:   2025-04-26
 * @Version: V1.0
 */
@Data
@TableName("driver")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="driver对象", description="driver")
public class Driver implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**dname*/
	@Excel(name = "dname", width = 15)
    @ApiModelProperty(value = "dname")
    private java.lang.String dname;
	/**dsex*/
	@Excel(name = "dsex", width = 15)
    @ApiModelProperty(value = "dsex")
    private java.lang.String dsex;
	/**dtel*/
	@Excel(name = "dtel", width = 15)
    @ApiModelProperty(value = "dtel")
    private java.lang.String dtel;
	/**dlic*/
	@Excel(name = "dlic", width = 15)
    @ApiModelProperty(value = "dlic")
    private java.lang.String dlic;
	/**dexp*/
	@Excel(name = "dexp", width = 15)
    @ApiModelProperty(value = "dexp")
    private java.lang.Integer dexp;
	/**dgrade*/
	@Excel(name = "dgrade", width = 15)
    @ApiModelProperty(value = "dgrade")
    private java.math.BigDecimal dgrade;
	/**dstatus*/
	@Excel(name = "dstatus", width = 15)
    @ApiModelProperty(value = "dstatus")
    private java.lang.String dstatus;
}
