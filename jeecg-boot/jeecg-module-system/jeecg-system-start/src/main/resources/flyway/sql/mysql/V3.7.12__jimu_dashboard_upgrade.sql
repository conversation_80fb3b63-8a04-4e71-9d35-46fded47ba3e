
ALTER TABLE `onl_drag_dataset_item`
ADD COLUMN `dict_table` varchar(125) NULL AFTER `dict_code`,
ADD COLUMN `dict_text` varchar(125) NULL AFTER `dict_table`;


-- 仪表盘默认文件夹目录树---
INSERT INTO `jimu_report_category`(`id`, `name`, `parent_id`, `iz_leaf`, `source_type`, `create_by`, `create_time`, `update_by`, `update_time`, `tenant_id`, `del_flag`) VALUES ('988299668956545024', '仪表盘设计', '0', 1, 'drag', '15931993294', '2024-08-27', '15931993294', '2024-08-28', NULL, 0);
INSERT INTO `jimu_report_category`(`id`, `name`, `parent_id`, `iz_leaf`, `source_type`, `create_by`, `create_time`, `update_by`, `update_time`, `tenant_id`, `del_flag`) VALUES ('988299695309357056', '门户设计', '0', 1, 'drag', '15931993294', '2024-08-27', '15931993294', '2024-08-27', NULL, 0);


ALTER TABLE `onl_drag_page`
MODIFY COLUMN `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属分类' AFTER `protection_code`;
UPDATE `onl_drag_page` SET `type` = '988299668956545024' WHERE `type` = '1';
UPDATE `onl_drag_page` SET `type` = '988299695309357056' WHERE `type` = '2';


INSERT INTO `onl_drag_comp` (`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1537773244027863041', '1537773378102984706', '圆形雷达图', 'JCircleRadar', 'tabler:radar', 100, NULL, '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 75,\n      \"name\": \"得分\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 65,\n      \"name\": \"篮板\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 55,\n      \"name\": \"防守\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 74,\n      \"name\": \"失误\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 38,\n      \"name\": \"盖帽\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 88,\n      \"name\": \"三分\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"圆形雷达图\",\r\n     \"textStyle\":{\r\n       \"fontWeight\":\"normal\"\r\n     },\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"radar\": [\n      {\n        \"indicator\": []\n      }\n    ],\n    \"series\": [\n      {\n        \"type\": \"radar\",\n        \"data\": []\n      }\n    ]\n  }\n}', '1', NULL, '2022-06-17 20:24:46', NULL, '2022-06-27 16:56:51');
INSERT INTO `onl_drag_comp` (`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1537318433201340417', '1537764868216684545', '金字塔漏斗图', 'JPyramidFunnel', 'icon-park-outline:children-pyramid', 100, NULL, '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/funnel\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1000,\n      \"name\": \"直接访问\"\n    },\n    {\n      \"value\": 200,\n      \"name\": \"邮件营销\"\n    },\n    {\n      \"value\": 400,\n      \"name\": \"联盟广告\"\n    },\n    {\n      \"value\": 600,\n      \"name\": \"网页查询\"\n    },\n    {\n      \"value\": 800,\n      \"name\": \"广告点击\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"基础漏斗图\",\r\n      \"textStyle\":{\r\n       \"fontWeight\":\"normal\"\r\n     },\n      \"show\": true\n    },\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"formatter\": \"{a} <br/>{b} : {c}%\"\n    },\n    \"legend\": {\n      \"orient\": \"horizontal\"\n    },\n    \"series\": [\n      {\n        \"name\": \"Funnel\",\n        \"type\": \"funnel\",\n        \"left\": \"10%\",\n        \"width\": \"80%\",\n        \"sort\": \"ascending\",\n        \"gap\": 2,\n        \"label\": {\n          \"show\": true,\n          \"position\": \"inside\"\n        },\n        \"labelLine\": {\n          \"length\": 10,\n          \"lineStyle\": {\n            \"width\": 1,\n            \"type\": \"solid\"\n          }\n        },\n        \"itemStyle\": {\n          \"borderColor\": \"#fff\",\n          \"borderWidth\": 1\n        },\n        \"emphasis\": {\n          \"label\": {\n            \"fontSize\": 20\n          }\n        }\n      }\n    ]\n  }\n}', '1', NULL, '2022-06-16 14:17:31', NULL, '2022-06-16 14:59:44');
INSERT INTO `onl_drag_comp` (`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('100100', '100', '查询条件', 'JForm', 'ant-design:form-outlined', 1, NULL, '{\n  \"w\": 24,\n  \"h\": 12,\n  \"dataType\": 1,\n  \"timeOut\": -1,\n  \"option\": {}\n}', '1', NULL, NULL, 'jeecg', '2022-04-29 18:55:15');
