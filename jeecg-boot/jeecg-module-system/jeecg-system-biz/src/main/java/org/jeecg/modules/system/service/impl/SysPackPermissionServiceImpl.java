package org.jeecg.modules.system.service.impl;

import org.jeecg.modules.system.entity.SysPackPermission;
import org.jeecg.modules.system.mapper.SysPackPermissionMapper;
import org.jeecg.modules.system.service.ISysPackPermissionService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 产品包菜单关系表
 * @Author: jeecg-boot
 * @Date:   2022-12-31
 * @Version: V1.0
 */
@Service
public class SysPackPermissionServiceImpl extends ServiceImpl<SysPackPermissionMapper, SysPackPermission> implements ISysPackPermissionService {

}
