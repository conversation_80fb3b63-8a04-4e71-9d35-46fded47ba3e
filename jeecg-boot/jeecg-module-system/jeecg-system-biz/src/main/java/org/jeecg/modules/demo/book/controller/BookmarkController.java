package org.jeecg.modules.demo.book.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.book.entity.Bookmark;
import org.jeecg.modules.demo.book.service.IBookmarkService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 书签表
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Api(tags="书签表")
@RestController
@RequestMapping("/book/bookmark")
@Slf4j
public class BookmarkController extends JeecgController<Bookmark, IBookmarkService> {
	@Autowired
	private IBookmarkService bookmarkService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bookmark
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "书签表-分页列表查询")
	@ApiOperation(value="书签表-分页列表查询", notes="书签表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Bookmark>> queryPageList(Bookmark bookmark,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("category", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("isDomestic", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<Bookmark> queryWrapper = QueryGenerator.initQueryWrapper(bookmark, req.getParameterMap(),customeRuleMap);
		Page<Bookmark> page = new Page<Bookmark>(pageNo, pageSize);
		IPage<Bookmark> pageList = bookmarkService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param bookmark
	 * @return
	 */
	@AutoLog(value = "书签表-添加")
	@ApiOperation(value="书签表-添加", notes="书签表-添加")
	@RequiresPermissions("book:bookmark:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Bookmark bookmark) {
		bookmarkService.save(bookmark);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param bookmark
	 * @return
	 */
	@AutoLog(value = "书签表-编辑")
	@ApiOperation(value="书签表-编辑", notes="书签表-编辑")
	@RequiresPermissions("book:bookmark:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Bookmark bookmark) {
		bookmarkService.updateById(bookmark);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "书签表-通过id删除")
	@ApiOperation(value="书签表-通过id删除", notes="书签表-通过id删除")
	@RequiresPermissions("book:bookmark:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bookmarkService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "书签表-批量删除")
	@ApiOperation(value="书签表-批量删除", notes="书签表-批量删除")
	@RequiresPermissions("book:bookmark:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bookmarkService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "书签表-通过id查询")
	@ApiOperation(value="书签表-通过id查询", notes="书签表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Bookmark> queryById(@RequestParam(name="id",required=true) String id) {
		Bookmark bookmark = bookmarkService.getById(id);
		if(bookmark==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bookmark);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param bookmark
    */
    @RequiresPermissions("book:bookmark:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Bookmark bookmark) {
        return super.exportXls(request, bookmark, Bookmark.class, "书签表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("book:bookmark:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Bookmark.class);
    }

}
