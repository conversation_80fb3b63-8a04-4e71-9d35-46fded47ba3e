package org.jeecg.modules.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.openapi.entity.OpenApiRecord;
import org.jeecg.modules.openapi.mapper.OpenApiRecordMapper;
import org.jeecg.modules.openapi.service.OpenApiRecordService;
import org.springframework.stereotype.Service;

/**
 * @date 2024/12/10 9:53
 */
@Service
public class OpenApiRecordServiceImpl extends ServiceImpl<OpenApiRecordMapper, OpenApiRecord> implements OpenApiRecordService {
}
