import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '标题',
    align:"center",
    dataIndex: 'title'
   },
   {
    title: '网址',
    align:"center",
    dataIndex: 'url'
   },
   {
    title: '图标',
    align:"center",
    dataIndex: 'icon',
    customRender:render.renderImage,
   },
   {
    title: '分类',
    align:"center",
    dataIndex: 'category_dictText'
   },
   {
    title: '是否国内网站',
    align:"center",
    dataIndex: 'isDomestic_dictText'
   },
   {
    title: '创建时间',
    align:"center",
    dataIndex: 'createTime'
   },
   {
    title: '更新时间',
    align:"center",
    dataIndex: 'updateTime'
   },
   {
    title: '描述',
    align:"center",
    dataIndex: 'description'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "标题",
      field: 'title',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "分类",
      field: 'category',
      component: 'JSelectMultiple',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "是否国内网站",
      field: 'isDomestic',
      component: 'JSelectMultiple',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "创建时间",
      field: 'createTime',
      component: 'DatePicker',
      componentProps: {
         showTime:true,
         valueFormat: 'YYYY-MM-DD HH:mm:ss'
       },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '标题',
    field: 'title',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入标题!'},
          ];
     },
  },
  {
    label: '网址',
    field: 'url',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入网址!'},
          ];
     },
  },
  {
    label: '图标',
    field: 'icon',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '分类',
    field: 'category',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '是否国内网站',
    field: 'isDomestic',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"",
        type: "radio"
     },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '更新时间',
    field: 'updateTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '描述',
    field: 'description',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  title: {title: '标题',order: 0,view: 'text', type: 'string',},
  url: {title: '网址',order: 1,view: 'text', type: 'string',},
  icon: {title: '图标',order: 2,view: 'image', type: 'string',},
  category: {title: '分类',order: 3,view: 'list', type: 'string',dictCode: '',},
  isDomestic: {title: '是否国内网站',order: 4,view: 'number', type: 'number',dictCode: '',},
  createTime: {title: '创建时间',order: 5,view: 'datetime', type: 'string',},
  updateTime: {title: '更新时间',order: 6,view: 'datetime', type: 'string',},
  description: {title: '描述',order: 7,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}