package org.jeecg.modules.demo.excel.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.excel.entity.Excel1;
import org.jeecg.modules.demo.excel.service.IExcelService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 导入解题需要的excel文件
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Api(tags="导入解题需要的excel文件")
@RestController
@RequestMapping("/excel/excel")
@Slf4j
public class ExcelController extends JeecgController<Excel1, IExcelService> {
	@Autowired
	private IExcelService excelService;
	
	/**
	 * 分页列表查询
	 *
	 * @param excel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "导入解题需要的excel文件-分页列表查询")
	@ApiOperation(value="导入解题需要的excel文件-分页列表查询", notes="导入解题需要的excel文件-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Excel1>> queryPageList(Excel1 excel,
											   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											   HttpServletRequest req) {
        QueryWrapper<Excel1> queryWrapper = QueryGenerator.initQueryWrapper(excel, req.getParameterMap());
		Page<Excel1> page = new Page<Excel1>(pageNo, pageSize);
		IPage<Excel1> pageList = excelService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param excel
	 * @return
	 */
	@AutoLog(value = "导入解题需要的excel文件-添加")
	@ApiOperation(value="导入解题需要的excel文件-添加", notes="导入解题需要的excel文件-添加")
	@RequiresPermissions("excel:excel:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Excel1 excel) {
		excelService.save(excel);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param excel
	 * @return
	 */
	@AutoLog(value = "导入解题需要的excel文件-编辑")
	@ApiOperation(value="导入解题需要的excel文件-编辑", notes="导入解题需要的excel文件-编辑")
	@RequiresPermissions("excel:excel:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Excel1 excel) {
		excelService.updateById(excel);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "导入解题需要的excel文件-通过id删除")
	@ApiOperation(value="导入解题需要的excel文件-通过id删除", notes="导入解题需要的excel文件-通过id删除")
	@RequiresPermissions("excel:excel:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		excelService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "导入解题需要的excel文件-批量删除")
	@ApiOperation(value="导入解题需要的excel文件-批量删除", notes="导入解题需要的excel文件-批量删除")
	@RequiresPermissions("excel:excel:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.excelService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	 /**
	  *  生成json格式的有效信息
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "导入解题需要的excel文件-生成json格式的有效信息")
	 @ApiOperation(value="导入解题需要的excel文件-生成json格式的有效信息", notes="导入解题需要的excel文件-生成json格式的有效信息")
	 @RequiresPermissions("excel:excel:genJson")
	 @GetMapping(value = "/genJson")
	 public Result<String> genJson(@RequestParam(name="id",required=true) String id) {
		 this.excelService.genJson(id);
		 return Result.OK("已生成json!");
	 }
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "导入解题需要的excel文件-通过id查询")
	@ApiOperation(value="导入解题需要的excel文件-通过id查询", notes="导入解题需要的excel文件-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Excel1> queryById(@RequestParam(name="id",required=true) String id) {
		Excel1 excel = excelService.getById(id);
		if(excel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(excel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param excel
    */
    @RequiresPermissions("excel:excel:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Excel1 excel) {
        return super.exportXls(request, excel, Excel1.class, "导入解题需要的excel文件");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("excel:excel:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Excel1.class);
    }

}
