package org.jeecg.modules.demo.excel.service.impl;

import org.jeecg.modules.demo.excel.entity.Excel1;
import org.jeecg.modules.demo.excel.mapper.ExcelMapper;
import org.jeecg.modules.demo.excel.service.IExcelService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 导入解题需要的excel文件
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Service
public class ExcelServiceImpl extends ServiceImpl<ExcelMapper, Excel1> implements IExcelService {


    @Override
    public void genJson(String id) {
        Excel1 excel = this.getById(id);
    }
}
